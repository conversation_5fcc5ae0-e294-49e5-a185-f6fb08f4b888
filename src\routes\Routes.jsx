import { Routes, Route } from 'react-router-dom';
import Dashboard from '../pages/Dashboard';
import Layout from '../layouts/Layout';
import Map from '../components/Map/Map';
import License from '../components/License';


const AppRoutes = () => {
  return (
    <Routes>
      <Route element={<Layout />}>
        <Route path="/" element={<Dashboard />} />
        {/* <Route path="/registration" element={<Registration />} /> */}
        {/* <Route path="/settings" element={<Settings />} /> */}
        <Route path="/license" element={<License />} />
        <Route path="/map" element={<Map/>} />
      </Route>
    </Routes>
  );
};

export default AppRoutes;
