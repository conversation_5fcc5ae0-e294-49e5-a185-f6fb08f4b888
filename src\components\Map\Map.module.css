/* Parent wrapper to prevent scroll propagation */
.mapWrapper {
    position: relative;
    height: 100vh;
    width: 100%;
    overflow: hidden;
}

/* Controls container */
.controlsContainer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 1rem;
    pointer-events: none;
}

/* Make individual controls clickable */
.controlsContainer > * {
    pointer-events: auto;
}

/* Filter container */
.filterContainer {
    position: absolute;
    top: 1rem;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
    padding: 8px 16px;
}

/* Button container */
.buttonContainer {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
}

.mapContainer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    background: #fff;
    overflow: hidden;
    isolation: isolate;
    z-index: 1;
}

/* Prevent scroll propagation */
.mapContainer :global(.leaflet-container) {
    height: 100% !important;
    width: 100% !important;
    position: absolute;
    isolation: isolate;
}

/* Hide attribution control */
.mapContainer :global(.leaflet-control-attribution) {
    display: none;
}

.mapContainer :global(.leaflet-control-zoom) {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    border-radius: 4px;
    margin-right: 20px;
    margin-top: 20px;
}

.mapContainer :global(.leaflet-control-zoom-in),
.mapContainer :global(.leaflet-control-zoom-out) {
    background: white !important;
    border: none !important;
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 20px;
    color: #666;
}

.mapContainer :global(.leaflet-control-zoom-in) {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-bottom: 1px solid #eee !important;
}

.mapContainer :global(.leaflet-control-zoom-out) {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

.mapContainer :global(.leaflet-control-zoom-in:hover),
.mapContainer :global(.leaflet-control-zoom-out:hover) {
    background: #f8f8f8 !important;
    color: #333;
}
