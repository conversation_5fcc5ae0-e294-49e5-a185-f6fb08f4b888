import React, { useEffect, useState } from 'react';
import Drawer from 'react-modern-drawer';
import 'react-modern-drawer/dist/index.css';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup, useMap } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import styles from './Map.module.css';
import L from 'leaflet';
import recordData from '../../data/record_data.json';
// Import all icons
import icon1 from '../../assets/icon1.svg';
import icon2 from '../../assets/icon2.svg';
import icon3 from '../../assets/icon3.svg';
import icon12 from '../../assets/icon12.svg';
import icon21 from '../../assets/icon21.svg';
import icon22 from '../../assets/icon22.svg';
import icon23 from '../../assets/icon23.svg';
import icon31 from '../../assets/icon31.svg';
import icon32 from '../../assets/icon32.svg';
import FilterComponent from '../forms/FilterComponent';
import DisplayDetailsOfAbnormality from '../DisplayDetailsOfAbnormality';

// Create custom icons for different traffic levels
const createCustomIcon = (iconUrl) => {
    return L.icon({
        iconUrl: iconUrl,
        iconSize: [24, 24],
        iconAnchor: [12, 24],
        popupAnchor: [0, -24]
    });
};

// Custom marker component with zoom functionality
const MarkerWithZoom = ({ position, icon, children,toggleDrawer }) => {
    const map = useMap();
    
    const handleMarkerClick = () => {
        map.setView(position, 20, {
            animate: true,
            duration: 1.5
        });
    };

    return (
        <Marker
            position={position}
            icon={icon}
            eventHandlers={{
                    click: () => {
      handleMarkerClick();
      toggleDrawer();
    }
            }}
        >
            {children}
        </Marker>
    );
};

// Create icon mapping based on priority and registration type
const getIcon = (priority, registType) => {
    const priorityLevel = {
        1: 'high',
        2: 'medium',
        3: 'low'
    };

    const registTypeMap = {
        0: 'manual',
        1: 'sensor',
        2: 'image_recognition'
    };

    const iconMap = {
        high: {
            manual: createCustomIcon(icon23),
            sensor: createCustomIcon(icon2),
            image_recognition: createCustomIcon(icon31)
        },
        medium: {
            manual: createCustomIcon(icon21),
            sensor: createCustomIcon(icon12),
            image_recognition: createCustomIcon(icon3)
        },
        low: {
            manual: createCustomIcon(icon22),
            sensor: createCustomIcon(icon1),
            image_recognition: createCustomIcon(icon32)
        }
    };

    const level = priorityLevel[priority] || 'medium';
    const type = registTypeMap[registType] || 'manual';
    return iconMap[level]?.[type] || createCustomIcon(icon1);
};

const Map = ({toggleDrawer}) => {
 
    const [map, setMap] = useState(null);

    useEffect(() => {
        if (map) {
            // Add zoom control to the right side
            L.control.zoom({
                position: 'topright'
            }).addTo(map);
        }
    }, [map]);
    
    // India's center coordinates
    const center = [20.5937, 78.9629];
    const zoom = 5;



    return (
        <>
        <div className={styles.mapWrapper}>
            <MapContainer 
            center={center} 
            zoom={zoom} 
            minZoom={2}
            maxZoom={20}
            preferCanvas={true}
            zoomControl={false}
            scrollWheelZoom={true}
            doubleClickZoom={true}
            style={{ height: '100%', width: '100%', background: '#f8f9fa' }}
            className={styles.mapContainer}
            ref={setMap}
        >
            <TileLayer
                url="https://tiles.stadiamaps.com/tiles/osm_bright/{z}/{x}/{y}{r}.png"
                attribution='&copy; <a href="https://stadiamaps.com/">Stadia Maps</a>, &copy; <a href="https://openmaptiles.org/">OpenMapTiles</a> &copy; <a href="http://openstreetmap.org">OpenStreetMap</a> contributors'
                maxNativeZoom={20}
                maxZoom={20}
                tileSize={256}
            />

            {/* Render record data markers */}
            {recordData.record_data.map((record, index) => (
                <MarkerWithZoom
                    key={`record-${index}`}
                    position={[record.GpsLat, record.GpsLon]}
                    icon={getIcon(record.Priority, record.RegistType)}
                    toggleDrawer={toggleDrawer}
                >
                    <Popup>
                        <strong>{record.CompanyName}</strong><br/>
                        {record.Comment}<br/>
                        <small>{record.Address}</small><br/>
                        <small>Priority: {record.Priority === 1 ? 'High' : record.Priority === 2 ? 'Medium' : 'Low'}</small><br/>
                        <small>Type: {record.RegistType === 0 ? 'Manual' : record.RegistType === 1 ? 'Sensor' : 'Image Recognition'}</small>
                    </Popup>
                </MarkerWithZoom>
            ))}
        </MapContainer>

            {/* Controls overlay */}
            <div className={`fixed top-4 transform -translate-x-1/2 z-40 ${isSidebarExpanded && isDrawerOpen ? "left-[1028px]" : "left-1/2 "}`}>
                
                    <FilterComponent />
               
            </div>

        </div>
        </>
    );
};

export default Map;
