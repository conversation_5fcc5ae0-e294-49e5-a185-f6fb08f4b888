// Dashboard.jsx (or any parent component)
import { useState } from "react";
import Drawer from "react-modern-drawer";
import "react-modern-drawer/dist/index.css";
import DisplayDetailsOfAbnormality from "../components/DisplayDetailsOfAbnormality";
import FilterComponent from "../components/forms/FilterComponent";
import { useSidebar } from "../layouts/Layout";
import Container from '../components/Container'
import Map from "../components/Map/Map";

const Dashboard = () => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const { isSidebarExpanded } = useSidebar();

  const toggleDrawer = () => {
    setIsDrawerOpen((prev) => !prev);
  };

  // Adjust drawer size based on sidebar state
  const drawerSize = isSidebarExpanded ? 790 : 536;

  return (
    <div>
      <Map toggleDrawer={toggleDrawer}>
      {/* <div className={`fixed top-4 transform -translate-x-1/2 z-40 ${isSidebarExpanded && isDrawerOpen ? "left-[1028px]" : "left-1/2 "}`}>
        <FilterComponent />
      </div> */}
      {/* <button
        onClick={toggleDrawer}
        className="bg-blue-600 text-white px-4 py-2 rounded"
      >
        Show Abnormality Details
      </button>

      <Drawer
        open={isDrawerOpen}
        onClose={toggleDrawer}
        direction="left"
        className="drawer"
        size={drawerSize}
        duration={600}
        enableOverlay={false}
        
      >
        <DisplayDetailsOfAbnormality onClose={toggleDrawer} />
      </Drawer> */}
      <div className="dashboard-container relative"></div>
      </Map>
      
      <Drawer
        open={isDrawerOpen}
        onClose={toggleDrawer}
        direction="left"
        className="drawer"
        size={drawerSize}
        duration={600}
        enableOverlay={false}
        
      >
        <DisplayDetailsOfAbnormality onClose={toggleDrawer} />
      </Drawer> 
    </div>

  );
};

export default Dashboard;
